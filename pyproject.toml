[project]
name = "autoagentsai"
version = "0.1.14"
description = "SDK for interacting with Autoagents.ai API"
readme = "README.md"
authors = [
    { name = "Hehua Fan", email = "<EMAIL>" }
]
requires-python = ">=3.11"
dependencies = [
    "pydantic>=2.11.7",
    "requests>=2.32.4",
]

[project.scripts]
autoagents-python-sdk = "autoagents_python_sdk:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
