#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AutoAgents AI 知识库客户端使用示例

本文件提供了 KbClient 的各种使用示例，展示了如何：
1. 管理知识库（创建、查询、更新、删除）
2. 管理文档（上传、列表、删除）
3. 查询知识库内容
"""

from autoagentsai.client import KbClient
from autoagentsai.uploader import create_file_like


def example_basic_usage():
    """基础使用示例"""
    print("📚 基础使用示例")
    print("-" * 40)
    
    # 初始化客户端
    kb_client = KbClient(
        personal_auth_key="your_personal_auth_key",
        personal_auth_secret="your_personal_auth_secret"
    )
    
    # 创建知识库
    kb_result = kb_client.create_kb(
        kb_name="产品文档库",
        kb_description="存储产品相关的技术文档和用户手册"
    )
    kb_id = kb_result["kbId"]
    print(f"✅ 知识库创建成功，ID: {kb_id}")
    
    # 上传文档
    content = """
    # 产品使用指南
    
    ## 快速开始
    1. 注册账户
    2. 登录系统
    3. 创建项目
    
    ## 功能介绍
    - 智能对话
    - 文档分析
    - 知识库管理
    """
    
    upload_result = kb_client.upload_document(
        kb_id=kb_id,
        content=content,
        file_name="product_guide.md"
    )
    print(f"✅ 文档上传成功")
    
    # 查询知识库
    results = kb_client.query(
        kb_id=kb_id,
        query="如何开始使用产品？"
    )
    
    for result in results:
        print(f"相关内容: {result['content'][:100]}...")


def example_file_upload():
    """文件上传示例"""
    print("\n📁 文件上传示例")
    print("-" * 40)
    
    kb_client = KbClient(
        personal_auth_key="your_personal_auth_key",
        personal_auth_secret="your_personal_auth_secret"
    )
    
    # 方式1: 上传本地文件
    kb_client.upload_document(
        kb_id="your_kb_id",
        files=["./document.pdf", "./manual.docx"]
    )
    
    # 方式2: 上传文件对象（适用于Web应用）
    with open("document.pdf", "rb") as f:
        file_content = f.read()
    
    file_obj = create_file_like(file_content, filename="document.pdf")
    kb_client.upload_document(
        kb_id="your_kb_id",
        files=[file_obj]
    )
    
    # 方式3: 上传文本内容
    kb_client.upload_document(
        kb_id="your_kb_id",
        content="这是文档的文本内容...",
        file_name="text_document.txt"
    )


def example_kb_management():
    """知识库管理示例"""
    print("\n🗂️ 知识库管理示例")
    print("-" * 40)
    
    kb_client = KbClient(
        personal_auth_key="your_personal_auth_key",
        personal_auth_secret="your_personal_auth_secret"
    )
    
    # 获取知识库列表
    kb_list = kb_client.list_kb(page_size=10)
    print(f"共有 {len(kb_list)} 个知识库")
    
    for kb in kb_list:
        print(f"- {kb['name']} (ID: {kb['kbId']})")
    
    # 获取特定知识库信息
    kb_info = kb_client.get_kb("your_kb_id")
    print(f"知识库名称: {kb_info['name']}")
    print(f"文档数量: {kb_info.get('documentCount', 0)}")
    
    # 更新知识库
    kb_client.update_kb(
        kb_id="your_kb_id",
        kb_name="新的知识库名称",
        kb_description="更新后的描述"
    )
    
    # 删除知识库（谨慎使用）
    # kb_client.delete_kb("your_kb_id")


def example_document_management():
    """文档管理示例"""
    print("\n📄 文档管理示例")
    print("-" * 40)
    
    kb_client = KbClient(
        personal_auth_key="your_personal_auth_key",
        personal_auth_secret="your_personal_auth_secret"
    )
    
    # 获取文档列表
    documents = kb_client.list_documents("your_kb_id")
    print(f"知识库中有 {len(documents)} 个文档")
    
    for doc in documents:
        print(f"- {doc['fileName']} (状态: {doc['processStatus']})")
        print(f"  上传时间: {doc.get('uploadTime', 'Unknown')}")
        print(f"  文件大小: {doc.get('fileSize', 'Unknown')}")
    
    # 删除文档
    # kb_client.delete_document("your_kb_id", "document_id")


def example_advanced_query():
    """高级查询示例"""
    print("\n🔍 高级查询示例")
    print("-" * 40)
    
    kb_client = KbClient(
        personal_auth_key="your_personal_auth_key",
        personal_auth_secret="your_personal_auth_secret"
    )
    
    # 基础查询
    results = kb_client.query(
        kb_id="your_kb_id",
        query="API接口如何调用？"
    )
    
    # 高精度查询
    high_quality_results = kb_client.query(
        kb_id="your_kb_id",
        query="用户认证流程",
        top_k=3,
        score_threshold=0.7  # 只返回高相似度结果
    )
    
    # 批量查询
    queries = [
        "如何安装软件？",
        "配置文件在哪里？",
        "如何备份数据？",
        "故障排除方法"
    ]
    
    for query in queries:
        print(f"\n查询: {query}")
        results = kb_client.query("your_kb_id", query, top_k=2)
        
        if results:
            for i, result in enumerate(results, 1):
                print(f"  {i}. 相似度: {result['score']:.3f}")
                print(f"     来源: {result['fileName']}")
                print(f"     内容: {result['content'][:100]}...")
        else:
            print("  未找到相关内容")


def example_web_integration():
    """Web应用集成示例"""
    print("\n🌐 Web应用集成示例")
    print("-" * 40)
    
    # 这是一个FastAPI集成示例
    example_code = '''
from fastapi import FastAPI, UploadFile, File, Form
from fastapi.responses import JSONResponse
from autoagentsai.client import KbClient
from autoagentsai.uploader import create_file_like

app = FastAPI()

# 初始化知识库客户端
kb_client = KbClient(
    personal_auth_key="your_key",
    personal_auth_secret="your_secret"
)

@app.post("/upload-document")
async def upload_document(
    kb_id: str = Form(...),
    file: UploadFile = File(...)
):
    """上传文档到知识库"""
    try:
        # 读取上传的文件
        content = await file.read()
        file_obj = create_file_like(content, filename=file.filename)
        
        # 上传到知识库
        result = kb_client.upload_document(
            kb_id=kb_id,
            files=[file_obj]
        )
        
        return JSONResponse(content={
            "success": True,
            "message": "文档上传成功",
            "data": result
        })
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": str(e)}
        )

@app.post("/query-kb")
async def query_knowledge_base(
    kb_id: str = Form(...),
    query: str = Form(...),
    top_k: int = Form(5)
):
    """查询知识库"""
    try:
        results = kb_client.query(
            kb_id=kb_id,
            query=query,
            top_k=top_k
        )
        
        return JSONResponse(content={
            "success": True,
            "data": results
        })
    except Exception as e:
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": str(e)}
        )
    '''
    
    print("FastAPI集成代码示例:")
    print(example_code)


def example_error_handling():
    """错误处理示例"""
    print("\n⚠️ 错误处理示例")
    print("-" * 40)
    
    kb_client = KbClient(
        personal_auth_key="your_personal_auth_key",
        personal_auth_secret="your_personal_auth_secret"
    )
    
    try:
        # 尝试获取不存在的知识库
        kb_info = kb_client.get_kb("non_existent_kb_id")
    except Exception as e:
        print(f"获取知识库失败: {e}")
    
    try:
        # 尝试上传无效参数
        kb_client.upload_document(
            kb_id="your_kb_id"
            # 缺少files或content参数
        )
    except ValueError as e:
        print(f"参数错误: {e}")
    except Exception as e:
        print(f"上传失败: {e}")
    
    try:
        # 尝试查询不存在的知识库
        results = kb_client.query("non_existent_kb_id", "test query")
    except Exception as e:
        print(f"查询失败: {e}")


if __name__ == "__main__":
    print("🧪 AutoAgents AI 知识库客户端使用示例")
    print("=" * 60)
    
    # 注意：这些示例需要有效的API密钥才能运行
    print("⚠️ 注意：运行这些示例需要有效的API密钥")
    print("请将 'your_personal_auth_key' 和 'your_personal_auth_secret' 替换为实际的密钥")
    print()
    
    # 显示各种使用示例
    example_basic_usage()
    example_file_upload()
    example_kb_management()
    example_document_management()
    example_advanced_query()
    example_web_integration()
    example_error_handling()
    
    print("\n" + "=" * 60)
    print("📖 更多信息请参考官方文档")
