#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试知识库相关模块的导入是否正常
"""

def test_imports():
    """测试所有知识库相关的导入"""
    print("🧪 测试知识库模块导入...")
    
    try:
        # 测试从主模块导入
        print("1. 测试从主模块导入...")
        from autoagentsai import KbClient
        from autoagentsai import KbCreateRequest, KbUpdateRequest, KbDocumentUploadRequest
        from autoagentsai import KbDocumentDeleteRequest, KbQueryRequest
        print("   ✅ 主模块导入成功")
        
        # 测试从子模块导入
        print("2. 测试从子模块导入...")
        from autoagentsai.client import KbClient as KbClient2
        from autoagentsai.kb_models import KbCreateRequest as KbCreateRequest2
        from autoagentsai import kb_api
        print("   ✅ 子模块导入成功")
        
        # 测试客户端初始化
        print("3. 测试客户端初始化...")
        # 注意：这里使用测试密钥，实际使用时请替换
        try:
            kb_client = KbClient(
                personal_auth_key="test_key",
                personal_auth_secret="test_secret"
            )
            print("   ⚠️ 客户端初始化需要有效的API密钥")
        except Exception as e:
            print(f"   ⚠️ 客户端初始化失败（预期的，因为使用了测试密钥）: {str(e)[:50]}...")
        
        # 测试模型创建
        print("4. 测试模型创建...")
        create_req = KbCreateRequest(
            name="测试知识库",
            description="这是一个测试知识库",
            type="document"
        )
        print(f"   ✅ 创建请求模型: {create_req.name}")
        
        update_req = KbUpdateRequest(
            kbId="test_kb_id",
            name="更新后的名称"
        )
        print(f"   ✅ 更新请求模型: {update_req.kbId}")
        
        upload_req = KbDocumentUploadRequest(
            kbId="test_kb_id",
            fileName="test.txt",
            fileContent="测试内容"
        )
        print(f"   ✅ 上传请求模型: {upload_req.fileName}")
        
        delete_req = KbDocumentDeleteRequest(
            kbId="test_kb_id",
            documentId="test_doc_id"
        )
        print(f"   ✅ 删除请求模型: {delete_req.documentId}")
        
        query_req = KbQueryRequest(
            kbId="test_kb_id",
            query="测试查询",
            topK=5,
            scoreThreshold=0.7
        )
        print(f"   ✅ 查询请求模型: {query_req.query}")
        
        # 测试API函数存在性
        print("5. 测试API函数...")
        api_functions = [
            'create_kb_api', 'get_kb_api', 'list_kb_api', 
            'update_kb_api', 'delete_kb_api', 'upload_document_api',
            'list_documents_api', 'delete_document_api', 'query_kb_api'
        ]
        
        for func_name in api_functions:
            if hasattr(kb_api, func_name):
                print(f"   ✅ {func_name} 存在")
            else:
                print(f"   ❌ {func_name} 不存在")
        
        print("\n🎉 所有导入测试完成！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False


def test_kb_client_methods():
    """测试KbClient的方法是否存在"""
    print("\n📚 测试KbClient方法...")
    
    try:
        from autoagentsai.client import KbClient
        
        # 检查方法是否存在
        methods = [
            'create_kb', 'get_kb', 'list_kb', 'update_kb', 'delete_kb',
            'upload_document', 'list_documents', 'delete_document', 'query'
        ]
        
        for method_name in methods:
            if hasattr(KbClient, method_name):
                print(f"   ✅ {method_name} 方法存在")
            else:
                print(f"   ❌ {method_name} 方法不存在")
        
        print("✅ KbClient方法检查完成")
        return True
        
    except Exception as e:
        print(f"❌ KbClient方法检查失败: {e}")
        return False


def test_original_imports():
    """测试原有的导入是否仍然正常"""
    print("\n🔄 测试原有模块导入...")
    
    try:
        # 测试聊天客户端
        from autoagentsai.client import ChatClient
        from autoagentsai import ChatRequest, ImageInput, FileInput
        print("   ✅ ChatClient 和相关模型导入成功")
        
        # 测试API模块
        from autoagentsai import api
        print("   ✅ api 模块导入成功")
        
        # 测试上传器
        from autoagentsai import FileUploader, create_file_like
        print("   ✅ 上传器模块导入成功")
        
        print("✅ 原有模块导入检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 原有模块导入失败: {e}")
        return False


if __name__ == "__main__":
    print("🧪 AutoAgents AI 知识库模块拆分测试")
    print("=" * 60)
    
    success = True
    
    # 运行各项测试
    success &= test_imports()
    success &= test_kb_client_methods()
    success &= test_original_imports()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！模块拆分成功！")
        print("\n📁 新的文件结构:")
        print("   - src/autoagentsai/kb_api.py      # 知识库API函数")
        print("   - src/autoagentsai/kb_models.py   # 知识库数据模型")
        print("   - src/autoagentsai/api.py         # 聊天API函数（已清理）")
        print("   - src/autoagentsai/models.py      # 聊天数据模型（已清理）")
        print("   - src/autoagentsai/client/KbClient.py  # 知识库客户端")
    else:
        print("❌ 部分测试失败，请检查代码")
