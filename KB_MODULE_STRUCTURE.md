# AutoAgents AI 知识库模块结构说明

## 📁 文件结构

经过重构，知识库相关的代码已经从原有的 `api.py` 和 `models.py` 中拆分出来，形成了独立的模块：

```
src/autoagentsai/
├── api.py                    # 聊天相关API函数（已清理）
├── models.py                 # 聊天相关数据模型（已清理）
├── kb_api.py                 # 🆕 知识库API函数
├── kb_models.py              # 🆕 知识库数据模型
├── client/
│   ├── ChatClient.py         # 聊天客户端
│   └── KbClient.py           # 知识库客户端
└── __init__.py               # 更新了导入配置
```

## 📋 模块详情

### 1. `kb_models.py` - 知识库数据模型

包含所有知识库相关的 Pydantic 模型：

- `KbCreateRequest` - 创建知识库请求
- `KbUpdateRequest` - 更新知识库请求  
- `KbDocumentUploadRequest` - 文档上传请求
- `KbDocumentDeleteRequest` - 文档删除请求
- `KbQueryRequest` - 知识库查询请求

### 2. `kb_api.py` - 知识库API函数

包含所有知识库相关的底层API调用函数：

**知识库管理：**
- `create_kb_api()` - 创建知识库
- `get_kb_api()` - 获取知识库信息
- `list_kb_api()` - 获取知识库列表
- `update_kb_api()` - 更新知识库
- `delete_kb_api()` - 删除知识库

**文档管理：**
- `upload_document_api()` - 上传文档
- `list_documents_api()` - 获取文档列表
- `delete_document_api()` - 删除文档
- `query_kb_api()` - 查询知识库

### 3. `client/KbClient.py` - 知识库客户端

高级封装的知识库客户端类，提供用户友好的接口：

**主要方法：**
- `create_kb()` - 创建知识库
- `get_kb()` - 获取知识库信息
- `list_kb()` - 获取知识库列表
- `update_kb()` - 更新知识库
- `delete_kb()` - 删除知识库
- `upload_document()` - 上传文档
- `list_documents()` - 获取文档列表
- `delete_document()` - 删除文档
- `query()` - 查询知识库

## 🔄 导入方式

### 从主模块导入（推荐）

```python
# 导入客户端
from autoagentsai import KbClient

# 导入数据模型（如果需要）
from autoagentsai import KbCreateRequest, KbUpdateRequest

# 导入API模块（如果需要底层调用）
from autoagentsai import kb_api
```

### 从子模块导入

```python
# 直接从子模块导入
from autoagentsai.client import KbClient
from autoagentsai.kb_models import KbCreateRequest
from autoagentsai.kb_api import create_kb_api
```

## 📝 使用示例

### 基础使用

```python
from autoagentsai import KbClient

# 初始化客户端
kb_client = KbClient(
    personal_auth_key="your_key",
    personal_auth_secret="your_secret"
)

# 创建知识库
result = kb_client.create_kb(
    kb_name="我的知识库",
    kb_description="测试知识库"
)
kb_id = result["kbId"]

# 上传文档
kb_client.upload_document(
    kb_id=kb_id,
    content="这是文档内容...",
    file_name="test.txt"
)

# 查询知识库
results = kb_client.query(
    kb_id=kb_id,
    query="查询问题"
)
```

## ✅ 兼容性

- ✅ 原有的 `ChatClient` 功能完全不受影响
- ✅ 原有的导入方式继续有效
- ✅ 新增的知识库功能独立工作
- ✅ 所有现有代码无需修改

## 🧪 测试

运行测试文件验证模块拆分是否成功：

```bash
python test_kb_import.py
```

测试内容包括：
- 模块导入测试
- 客户端方法检查
- 原有功能兼容性测试

## 📚 相关文件

- `test_kb_client.py` - 完整的功能测试示例
- `kb_client_examples.py` - 各种使用场景示例
- `test_kb_import.py` - 模块导入测试

## 🎯 优势

1. **模块化设计** - 知识库功能独立，便于维护
2. **清晰的职责分离** - 聊天和知识库功能分离
3. **向后兼容** - 不影响现有代码
4. **易于扩展** - 新功能可以独立添加
5. **代码复用** - 底层API可以被其他模块使用

## 🔧 维护说明

- 知识库相关的新功能应添加到 `kb_api.py` 和 `kb_models.py`
- 聊天相关的功能继续在 `api.py` 和 `models.py` 中维护
- 客户端类的更新分别在对应的文件中进行
- 新增导出需要更新 `__init__.py` 文件
