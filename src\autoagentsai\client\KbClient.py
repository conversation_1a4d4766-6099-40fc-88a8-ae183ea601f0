from typing import Optional, List, Dict, Union, IO
from ..api import get_jwt_token_api
from ..kb_api import (
    create_kb_api, get_kb_api, list_kb_api,
    update_kb_api, delete_kb_api, upload_document_api,
    list_documents_api, delete_document_api, query_kb_api
)


class KbClient:
    def __init__(self, personal_auth_key: str, personal_auth_secret: str, base_url: str = "https://uat.agentspro.cn"):
        """
        AutoAgents AI 知识库客户端

        用于与 AutoAgents AI 平台进行知识库管理的主要客户端类。
        支持知识库的创建、更新、删除，以及文档的上传、查询等功能。

        Args:
            personal_auth_key (str): 认证密钥
                - 获取方式：右上角 - 个人密钥

            personal_auth_secret (str): 认证密钥
                - 获取方式：右上角 - 个人密钥

            base_url (str, optional): API 服务基础地址
                - 默认值: "https://uat.agentspro.cn"
                - 测试环境: "https://uat.agentspro.cn"
                - 生产环境: "https://agentspro.cn"
                - 私有部署时可指定自定义地址
        """
        self.jwt_token = get_jwt_token_api(personal_auth_key, personal_auth_secret, base_url)
        self.base_url = base_url

    def create_kb(
        self,
        name: str,
        description: str = "",
        parent_id: int = 0,
        avatar_url: Optional[str] = None,
        vector_model: Optional[str] = None,
        kb_type: str = "kb",
        tags: Optional[List[str]] = None,
        ext_config: Optional[Dict[str, str]] = None
    ) -> Dict[str, str]:
        """
        创建知识库

        创建一个新的知识库，用于存储和管理文档、问答等知识内容。

        Args:
            name (str): 知识库名称
                - 必填参数，知识库的显示名称
                - 建议使用有意义的名称，便于管理和识别

            description (str, optional): 知识库描述
                - 可选参数，默认为空字符串
                - 用于描述知识库的用途、内容范围等
                - 有助于团队协作和知识库管理

            parent_id (int, optional): 父文件夹ID
                - 可选参数，默认为0（根目录）
                - 用于组织知识库的层级结构

            avatar_url (str, optional): 知识库头像URL
                - 可选参数，知识库的图标地址
                - 用于个性化显示

            vector_model (str, optional): 向量模型
                - 可选参数，指定使用的向量化模型
                - 影响文档的向量化和搜索效果

            kb_type (str, optional): 知识库类型
                - 可选参数，默认为 "kb"
                - 支持类型：
                  - "kb": 知识库类型
                  - "folder": 文件夹类型

            tags (List[str], optional): 知识库标签
                - 可选参数，用于分类和检索
                - 示例: ["技术文档", "产品手册"]

            ext_config (Dict, optional): 扩展配置
                - 可选参数，高级配置选项
                - 包含分块、搜索、重排序等配置

        Returns:
            Dict[str, str]: 创建结果信息
                - 包含新创建的知识库ID、状态等信息
                - 示例: {"kbId": "123456", "status": "success"}

        示例:
            Example 1: 创建基础知识库
            .. code-block:: python

                from autoagentsai.client import KbClient
                kb_client = KbClient(
                    personal_auth_key="your_personal_auth_key",
                    personal_auth_secret="your_personal_auth_secret"
                )
                result = kb_client.create_kb(
                    name="产品文档库",
                    description="存储产品相关的技术文档和用户手册"
                )
                print(f"知识库ID: {result['kbId']}")

            Example 2: 创建带标签的知识库
            .. code-block:: python

                result = kb_client.create_kb(
                    name="技术支持库",
                    description="技术支持相关文档",
                    tags=["技术支持", "FAQ"],
                    vector_model="text-embedding-ada-002"
                )

            Example 3: 创建高级配置的知识库
            .. code-block:: python

                ext_config = {
                    "chunkSize": 500,
                    "similarity": 0.75,
                    "search": {
                        "topK": 5,
                        "enableRerank": True
                    }
                }

                result = kb_client.create_kb(
                    name="高级知识库",
                    description="带有自定义配置的知识库",
                    ext_config=ext_config
                )

        注意:
            - 知识库名称在同一账户下应保持唯一性
            - 创建后可通过 update_kb() 方法修改名称和描述
            - 删除知识库会同时删除其中的所有文档，请谨慎操作
            - 扩展配置会影响文档处理和搜索效果
        """
        return create_kb_api(
            jwt_token=self.jwt_token,
            base_url=self.base_url,
            name=name,
            description=description,
            parent_id=parent_id,
            avatar_url=avatar_url,
            vector_model=vector_model,
            kb_type=kb_type,
            tags=tags,
            ext_config=ext_config
        )

    def create_kb_simple(
        self,
        kb_name: str,
        kb_description: str = "",
        kb_type: str = "kb"
    ) -> Dict[str, str]:
        """
        简化的创建知识库方法（向后兼容）

        这是一个简化版本的创建知识库方法，保持与旧版本的兼容性。

        Args:
            kb_name (str): 知识库名称
            kb_description (str, optional): 知识库描述，默认为空
            kb_type (str, optional): 知识库类型，默认为 "kb"

        Returns:
            Dict[str, str]: 创建结果信息

        示例:
            .. code-block:: python

                result = kb_client.create_kb_simple(
                    kb_name="简单知识库",
                    kb_description="这是一个简单的知识库"
                )
        """
        return self.create_kb(
            name=kb_name,
            description=kb_description,
            kb_type=kb_type
        )

    def get_kb(self, kb_id: str) -> Dict[str, str]:
        """
        获取知识库详细信息

        根据知识库ID获取知识库的详细信息，包括名称、描述、创建时间、文档数量等。

        Args:
            kb_id (str): 知识库ID
                - 必填参数，知识库的唯一标识符
                - 通过 create_kb() 创建时返回，或通过 list_kb() 获取

        Returns:
            Dict[str, str]: 知识库详细信息
                - 包含知识库的完整信息
                - 示例: {
                    "kbId": "kb_123456",
                    "name": "产品文档库",
                    "description": "存储产品相关文档",
                    "type": "document",
                    "createTime": "2024-01-01 10:00:00",
                    "documentCount": 15,
                    "status": "active"
                  }

        示例:
            Example 1: 获取知识库信息
            .. code-block:: python

                from autoagentsai.client import KbClient
                kb_client = KbClient(
                    personal_auth_key="your_personal_auth_key",
                    personal_auth_secret="your_personal_auth_secret"
                )
                kb_info = kb_client.get_kb("kb_123456")
                print(f"知识库名称: {kb_info['name']}")
                print(f"文档数量: {kb_info['documentCount']}")

        注意:
            - 如果知识库ID不存在，会抛出异常
            - 只能获取当前账户下的知识库信息
        """
        return get_kb_api(
            jwt_token=self.jwt_token,
            base_url=self.base_url,
            kb_id=kb_id
        )

    def list_kb(
        self,
        page_size: int = 20,
        page_number: int = 1
    ) -> List[Dict[str, str]]:
        """
        获取知识库列表

        获取当前账户下的所有知识库列表，支持分页查询。

        Args:
            page_size (int, optional): 每页数量
                - 可选参数，默认为20
                - 取值范围：1-100
                - 建议根据实际需要设置合适的页面大小

            page_number (int, optional): 页码
                - 可选参数，默认为1
                - 从1开始计数
                - 用于分页获取大量知识库数据

        Returns:
            List[Dict[str, str]]: 知识库列表
                - 每个元素包含知识库的基本信息
                - 示例: [
                    {
                        "kbId": "kb_123456",
                        "name": "产品文档库",
                        "description": "产品相关文档",
                        "type": "document",
                        "createTime": "2024-01-01 10:00:00",
                        "documentCount": 15
                    },
                    {
                        "kbId": "kb_789012",
                        "name": "FAQ知识库",
                        "description": "常见问题解答",
                        "type": "qa",
                        "createTime": "2024-01-02 14:30:00",
                        "documentCount": 8
                    }
                  ]

        示例:
            Example 1: 获取所有知识库
            .. code-block:: python

                from autoagentsai.client import KbClient
                kb_client = KbClient(
                    personal_auth_key="your_personal_auth_key",
                    personal_auth_secret="your_personal_auth_secret"
                )
                kb_list = kb_client.list_kb()
                for kb in kb_list:
                    print(f"知识库: {kb['name']} (ID: {kb['kbId']})")

            Example 2: 分页获取知识库
            .. code-block:: python

                # 获取第2页，每页10个
                kb_list = kb_client.list_kb(page_size=10, page_number=2)

        注意:
            - 返回结果按创建时间倒序排列（最新的在前）
            - 如果没有知识库，返回空列表 []
            - 只返回当前账户下的知识库
        """
        return list_kb_api(
            jwt_token=self.jwt_token,
            base_url=self.base_url,
            page_size=page_size,
            page_number=page_number
        )

    def update_kb(
        self,
        kb_id: str,
        kb_name: Optional[str] = None,
        kb_description: Optional[str] = None
    ) -> Dict[str, str]:
        """
        更新知识库信息

        更新指定知识库的名称和描述信息。

        Args:
            kb_id (str): 知识库ID
                - 必填参数，要更新的知识库唯一标识符

            kb_name (str, optional): 新的知识库名称
                - 可选参数，如果提供则更新名称
                - 建议使用有意义的名称

            kb_description (str, optional): 新的知识库描述
                - 可选参数，如果提供则更新描述
                - 可以设置为空字符串来清空描述

        Returns:
            Dict[str, str]: 更新后的知识库信息
                - 包含更新后的完整知识库信息

        示例:
            Example 1: 更新知识库名称
            .. code-block:: python

                from autoagentsai.client import KbClient
                kb_client = KbClient(
                    personal_auth_key="your_personal_auth_key",
                    personal_auth_secret="your_personal_auth_secret"
                )
                result = kb_client.update_kb(
                    kb_id="kb_123456",
                    kb_name="新的产品文档库"
                )

            Example 2: 同时更新名称和描述
            .. code-block:: python

                result = kb_client.update_kb(
                    kb_id="kb_123456",
                    kb_name="企业知识库",
                    kb_description="包含企业内部所有技术文档和流程说明"
                )

        注意:
            - 至少需要提供 kb_name 或 kb_description 中的一个参数
            - 如果知识库ID不存在，会抛出异常
            - 更新操作不会影响知识库中的文档内容
        """
        if kb_name is None and kb_description is None:
            raise ValueError("至少需要提供 kb_name 或 kb_description 中的一个参数")

        return update_kb_api(
            jwt_token=self.jwt_token,
            base_url=self.base_url,
            kb_id=kb_id,
            kb_name=kb_name,
            kb_description=kb_description
        )

    def delete_kb(self, kb_id: str) -> bool:
        """
        删除知识库

        删除指定的知识库及其包含的所有文档。此操作不可逆，请谨慎使用。

        Args:
            kb_id (str): 知识库ID
                - 必填参数，要删除的知识库唯一标识符

        Returns:
            bool: 删除是否成功
                - True: 删除成功
                - False: 删除失败（通常会抛出异常而不是返回False）

        示例:
            Example 1: 删除知识库
            .. code-block:: python

                from autoagentsai.client import KbClient
                kb_client = KbClient(
                    personal_auth_key="your_personal_auth_key",
                    personal_auth_secret="your_personal_auth_secret"
                )
                success = kb_client.delete_kb("kb_123456")
                if success:
                    print("知识库删除成功")

        注意:
            - 删除知识库会同时删除其中的所有文档和数据
            - 此操作不可逆，删除后无法恢复
            - 如果知识库正在被使用，可能无法删除
            - 建议在删除前先备份重要数据
        """
        return delete_kb_api(
            jwt_token=self.jwt_token,
            base_url=self.base_url,
            kb_id=kb_id
        )

    def upload_document(
        self,
        kb_id: str,
        files: Optional[List[Union[str, IO]]] = None,
        content: Optional[str] = None,
        file_name: Optional[str] = None
    ) -> Dict[str, str]:
        """
        上传文档到知识库

        向指定知识库上传文档，支持文件上传和文本内容上传两种方式。

        Args:
            kb_id (str): 知识库ID
                - 必填参数，目标知识库的唯一标识符

            files (List[Union[str, IO]], optional): 文件列表
                - 可选参数，与content参数二选一
                - 支持两种类型：
                  1. 文件路径字符串：如 "/path/to/document.pdf"
                  2. 文件对象：如 open("file.txt", "rb") 或 BytesIO 对象
                - 支持文件类型：PDF, TXT, DOC, DOCX, XLS, XLSX, PPT, PPTX, MD 等
                - 示例: ["./report.pdf", open("data.xlsx", "rb")]

            content (str, optional): 文本内容
                - 可选参数，与files参数二选一
                - 直接提供文本内容，适用于纯文本文档
                - 当使用此参数时，必须同时提供file_name参数

            file_name (str, optional): 文件名
                - 当使用content参数时为必填
                - 用于标识文档的名称和类型
                - 建议包含适当的文件扩展名，如 "document.txt"

        Returns:
            Dict[str, str]: 上传结果信息
                - 包含上传成功的文档信息
                - 示例: {
                    "documentId": "doc_123456",
                    "fileName": "report.pdf",
                    "status": "uploaded",
                    "processStatus": "processing"
                  }

        示例:
            Example 1: 上传本地文件
            .. code-block:: python

                from autoagentsai.client import KbClient
                kb_client = KbClient(
                    personal_auth_key="your_personal_auth_key",
                    personal_auth_secret="your_personal_auth_secret"
                )
                result = kb_client.upload_document(
                    kb_id="kb_123456",
                    files=["./report.pdf", "./manual.docx"]
                )
                print(f"上传成功，文档ID: {result['documentId']}")

            Example 2: 上传文本内容
            .. code-block:: python

                content = '''
                # 产品使用说明

                ## 功能介绍
                这是一个AI助手产品...

                ## 使用方法
                1. 登录系统
                2. 选择功能模块
                3. 开始使用
                '''

                result = kb_client.upload_document(
                    kb_id="kb_123456",
                    content=content,
                    file_name="product_manual.md"
                )

            Example 3: 上传文件对象
            .. code-block:: python

                from autoagentsai.uploader import create_file_like

                # 处理前端上传的文件
                file_obj = create_file_like(file_content, filename="upload.pdf")
                result = kb_client.upload_document(
                    kb_id="kb_123456",
                    files=[file_obj]
                )

        注意:
            - files 和 content 参数必须提供其中一个，不能同时为空
            - 使用 content 参数时必须提供 file_name
            - 文档上传后需要一定时间进行处理和索引
            - 大文件上传可能需要较长时间，请耐心等待
            - 支持的文件大小和格式可能有限制
        """
        if not files and not content:
            raise ValueError("必须提供 files 或 content 参数")
        if content and not file_name:
            raise ValueError("使用 content 参数时必须提供 file_name")
        if files and content:
            raise ValueError("files 和 content 参数不能同时提供")

        return upload_document_api(
            jwt_token=self.jwt_token,
            base_url=self.base_url,
            kb_id=kb_id,
            files=files,
            content=content,
            file_name=file_name
        )

    def list_documents(
        self,
        kb_id: str,
        page_size: int = 20,
        page_number: int = 1
    ) -> List[Dict[str, str]]:
        """
        获取知识库文档列表

        获取指定知识库中的所有文档列表，支持分页查询。

        Args:
            kb_id (str): 知识库ID
                - 必填参数，目标知识库的唯一标识符

            page_size (int, optional): 每页数量
                - 可选参数，默认为20
                - 取值范围：1-100

            page_number (int, optional): 页码
                - 可选参数，默认为1
                - 从1开始计数

        Returns:
            List[Dict[str, str]]: 文档列表
                - 每个元素包含文档的详细信息
                - 示例: [
                    {
                        "documentId": "doc_123456",
                        "fileName": "product_manual.pdf",
                        "fileSize": "2.5MB",
                        "uploadTime": "2024-01-01 10:00:00",
                        "processStatus": "completed",
                        "chunkCount": 25
                    }
                  ]

        示例:
            Example 1: 获取文档列表
            .. code-block:: python

                from autoagentsai.client import KbClient
                kb_client = KbClient(
                    personal_auth_key="your_personal_auth_key",
                    personal_auth_secret="your_personal_auth_secret"
                )
                documents = kb_client.list_documents("kb_123456")
                for doc in documents:
                    print(f"文档: {doc['fileName']} (状态: {doc['processStatus']})")

        注意:
            - 返回结果按上传时间倒序排列
            - processStatus 表示文档处理状态：processing(处理中)、completed(完成)、failed(失败)
            - 只有状态为 completed 的文档才能用于查询
        """
        return list_documents_api(
            jwt_token=self.jwt_token,
            base_url=self.base_url,
            kb_id=kb_id,
            page_size=page_size,
            page_number=page_number
        )

    def delete_document(self, kb_id: str, document_id: str) -> bool:
        """
        删除知识库文档

        从指定知识库中删除指定的文档。此操作不可逆，请谨慎使用。

        Args:
            kb_id (str): 知识库ID
                - 必填参数，文档所在知识库的唯一标识符

            document_id (str): 文档ID
                - 必填参数，要删除的文档唯一标识符
                - 可通过 list_documents() 方法获取

        Returns:
            bool: 删除是否成功
                - True: 删除成功
                - False: 删除失败（通常会抛出异常而不是返回False）

        示例:
            Example 1: 删除文档
            .. code-block:: python

                from autoagentsai.client import KbClient
                kb_client = KbClient(
                    personal_auth_key="your_personal_auth_key",
                    personal_auth_secret="your_personal_auth_secret"
                )
                success = kb_client.delete_document(
                    kb_id="kb_123456",
                    document_id="doc_789012"
                )
                if success:
                    print("文档删除成功")

        注意:
            - 删除文档会同时删除其相关的索引和向量数据
            - 此操作不可逆，删除后无法恢复
            - 删除文档可能影响知识库的查询结果
        """
        return delete_document_api(
            jwt_token=self.jwt_token,
            base_url=self.base_url,
            kb_id=kb_id,
            document_id=document_id
        )

    def query(
        self,
        kb_id: str,
        query: str,
        top_k: int = 5,
        score_threshold: float = 0.0
    ) -> List[Dict[str, str]]:
        """
        查询知识库

        在指定知识库中搜索与查询文本相关的内容，返回最相关的文档片段。

        Args:
            kb_id (str): 知识库ID
                - 必填参数，要查询的知识库唯一标识符

            query (str): 查询文本
                - 必填参数，用户的查询问题或关键词
                - 支持自然语言查询
                - 建议使用清晰、具体的描述

            top_k (int, optional): 返回结果数量
                - 可选参数，默认为5
                - 取值范围：1-20
                - 返回相似度最高的前K个结果

            score_threshold (float, optional): 相似度阈值
                - 可选参数，默认为0.0
                - 取值范围：0.0-1.0
                - 只返回相似度大于此阈值的结果
                - 设置较高阈值可以过滤低质量结果

        Returns:
            List[Dict[str, str]]: 查询结果列表
                - 按相似度从高到低排序
                - 每个元素包含匹配的文档片段信息
                - 示例: [
                    {
                        "documentId": "doc_123456",
                        "fileName": "product_manual.pdf",
                        "content": "产品使用说明：首先需要登录系统...",
                        "score": 0.85,
                        "chunkId": "chunk_001",
                        "pageNumber": 3
                    }
                  ]

        示例:
            Example 1: 基础查询
            .. code-block:: python

                from autoagentsai.client import KbClient
                kb_client = KbClient(
                    personal_auth_key="your_personal_auth_key",
                    personal_auth_secret="your_personal_auth_secret"
                )
                results = kb_client.query(
                    kb_id="kb_123456",
                    query="如何使用产品的登录功能？"
                )
                for result in results:
                    print(f"文档: {result['fileName']}")
                    print(f"内容: {result['content']}")
                    print(f"相似度: {result['score']}")
                    print("-" * 50)

            Example 2: 高精度查询
            .. code-block:: python

                # 只返回高相似度的结果
                results = kb_client.query(
                    kb_id="kb_123456",
                    query="API接口调用方法",
                    top_k=3,
                    score_threshold=0.7
                )

        注意:
            - 查询结果的质量取决于文档的内容和索引质量
            - 建议使用具体、清晰的查询词汇
            - 相似度分数越高表示匹配度越好
            - 如果没有找到相关内容，返回空列表
        """
        return query_kb_api(
            jwt_token=self.jwt_token,
            base_url=self.base_url,
            kb_id=kb_id,
            query=query,
            top_k=top_k,
            score_threshold=score_threshold
        )