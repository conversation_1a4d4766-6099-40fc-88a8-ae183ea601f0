#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AutoAgents AI 知识库客户端测试示例

本文件演示了如何使用 KbClient 进行知识库管理操作，包括：
- 创建知识库
- 上传文档
- 查询知识库
- 管理文档
"""

from autoagentsai.client import KbClient
from autoagentsai.uploader import create_file_like
import time


def test_kb_client():
    """测试知识库客户端的完整功能"""
    
    # 初始化客户端
    print("🚀 初始化知识库客户端...")
    kb_client = KbClient(
        personal_auth_key="e7a964a7e754413a9ea4bc1395a38d39",
        personal_auth_secret="r4wBtqVD1qjItzQapJudKQPFozHAS9eb"
    )
    print("✅ 客户端初始化成功")
    
    try:
        # 1. 创建知识库
        print("\n📚 创建知识库...")
        kb_result = kb_client.create_kb(
            kb_name="测试知识库",
            kb_description="用于SDK测试的知识库",
            kb_type="document"
        )
        kb_id = kb_result.get("kbId")
        print(f"✅ 知识库创建成功，ID: {kb_id}")
        
        # 2. 获取知识库列表
        print("\n📋 获取知识库列表...")
        kb_list = kb_client.list_kb()
        print(f"✅ 找到 {len(kb_list)} 个知识库")
        for kb in kb_list[:3]:  # 只显示前3个
            print(f"   - {kb.get('name', 'Unknown')} (ID: {kb.get('kbId', 'Unknown')})")
        
        # 3. 获取知识库详情
        print(f"\n🔍 获取知识库详情...")
        kb_info = kb_client.get_kb(kb_id)
        print(f"✅ 知识库名称: {kb_info.get('name', 'Unknown')}")
        print(f"   描述: {kb_info.get('description', 'No description')}")
        
        # 4. 上传文本文档
        print("\n📄 上传文本文档...")
        test_content = """
# AutoAgents AI 使用指南

## 简介
AutoAgents AI 是一个强大的人工智能平台，提供对话、知识库等多种功能。

## 主要功能
1. **智能对话**: 支持多轮对话，理解上下文
2. **知识库管理**: 上传文档，构建专业知识库
3. **文件分析**: 支持PDF、Word、Excel等多种格式
4. **API集成**: 提供完整的SDK和API接口

## 使用方法
### 创建知识库
```python
from autoagentsai.client import KbClient
kb_client = KbClient(auth_key="your_key", auth_secret="your_secret")
result = kb_client.create_kb("我的知识库", "描述信息")
```

### 上传文档
```python
kb_client.upload_document(kb_id, files=["document.pdf"])
```

### 查询知识库
```python
results = kb_client.query(kb_id, "如何使用API？")
```

## 注意事项
- 确保API密钥的安全性
- 文档上传后需要一定时间进行处理
- 查询时使用具体、清晰的问题描述

## 技术支持
如有问题，请联系技术支持团队。
        """
        
        upload_result = kb_client.upload_document(
            kb_id=kb_id,
            content=test_content.strip(),
            file_name="autoagents_guide.md"
        )
        print(f"✅ 文档上传成功，文档ID: {upload_result.get('documentId', 'Unknown')}")
        
        # 5. 等待文档处理
        print("\n⏳ 等待文档处理...")
        time.sleep(5)  # 等待5秒让文档处理完成
        
        # 6. 获取文档列表
        print("\n📑 获取文档列表...")
        documents = kb_client.list_documents(kb_id)
        print(f"✅ 找到 {len(documents)} 个文档")
        for doc in documents:
            print(f"   - {doc.get('fileName', 'Unknown')} (状态: {doc.get('processStatus', 'Unknown')})")
        
        # 7. 查询知识库
        print("\n🔍 查询知识库...")
        queries = [
            "如何创建知识库？",
            "支持哪些文件格式？",
            "API的使用方法",
            "技术支持联系方式"
        ]
        
        for query in queries:
            print(f"\n❓ 查询: {query}")
            results = kb_client.query(
                kb_id=kb_id,
                query=query,
                top_k=2,
                score_threshold=0.1
            )
            
            if results:
                for i, result in enumerate(results, 1):
                    print(f"   {i}. 相似度: {result.get('score', 0):.3f}")
                    content = result.get('content', '')
                    # 截取前100个字符
                    preview = content[:100] + "..." if len(content) > 100 else content
                    print(f"      内容: {preview}")
            else:
                print("   ❌ 未找到相关内容")
        
        # 8. 更新知识库信息
        print(f"\n✏️ 更新知识库信息...")
        update_result = kb_client.update_kb(
            kb_id=kb_id,
            kb_name="AutoAgents AI 测试知识库",
            kb_description="更新后的描述：包含使用指南和API文档"
        )
        print(f"✅ 知识库更新成功")
        
        print(f"\n🎉 所有测试完成！知识库ID: {kb_id}")
        print("💡 提示：可以保留此知识库用于后续测试，或手动删除")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


def test_file_upload():
    """测试文件上传功能"""
    print("\n" + "="*60)
    print("📁 测试文件上传功能")
    print("="*60)
    
    kb_client = KbClient(
        personal_auth_key="e7a964a7e754413a9ea4bc1395a38d39",
        personal_auth_secret="r4wBtqVD1qjItzQapJudKQPFozHAS9eb"
    )
    
    try:
        # 创建测试知识库
        kb_result = kb_client.create_kb(
            kb_name="文件上传测试库",
            kb_description="用于测试文件上传功能"
        )
        kb_id = kb_result.get("kbId")
        print(f"✅ 测试知识库创建成功，ID: {kb_id}")
        
        # 检查是否有PDF文件可以测试
        import os
        pdf_files = [f for f in os.listdir('.') if f.endswith('.pdf')]
        
        if pdf_files:
            pdf_file = pdf_files[0]
            print(f"📄 找到PDF文件: {pdf_file}")
            
            # 上传PDF文件
            upload_result = kb_client.upload_document(
                kb_id=kb_id,
                files=[pdf_file]
            )
            print(f"✅ PDF文件上传成功")
            
            # 等待处理
            print("⏳ 等待文件处理...")
            time.sleep(10)
            
            # 查询测试
            query_result = kb_client.query(
                kb_id=kb_id,
                query="文档的主要内容是什么？",
                top_k=3
            )
            
            if query_result:
                print("✅ 查询成功，找到相关内容:")
                for i, result in enumerate(query_result, 1):
                    print(f"   {i}. 相似度: {result.get('score', 0):.3f}")
                    content = result.get('content', '')[:150]
                    print(f"      内容预览: {content}...")
            else:
                print("⚠️ 未找到相关内容，可能文档还在处理中")
        else:
            print("⚠️ 当前目录下没有PDF文件，跳过文件上传测试")
            
    except Exception as e:
        print(f"❌ 文件上传测试失败: {str(e)}")


if __name__ == "__main__":
    print("🧪 AutoAgents AI 知识库客户端测试")
    print("="*60)
    
    # 运行基础功能测试
    test_kb_client()
    
    # 运行文件上传测试
    test_file_upload()
    
    print("\n" + "="*60)
    print("🏁 测试完成")
