from typing import Optional, List, Dict
import requests
from .kb_models import KbQueryRequest


def create_kb_api(
    jwt_token: str,
    base_url: str,
    name: Optional[str] = None,
    description: Optional[str] = None,
    parent_id: Optional[int] = 0,
    avatar_url: Optional[str] = None,
    vector_model: Optional[str] = None,
    kb_type: Optional[str] = "kb",
    tags: Optional[List[str]] = None,
    ext_config: Optional[Dict] = None
) -> Dict[str, str]:
    """
    创建知识库 API

    Args:
        jwt_token (str): JWT 认证令牌
        base_url (str): API 服务基础地址
        name (str, optional): 知识库名称
        description (str, optional): 知识库描述
        parent_id (int, optional): 父文件夹ID，默认为0
        avatar_url (str, optional): 知识库头像URL
        vector_model (str, optional): 向量模型
        kb_type (str, optional): 知识库类型，"folder" 或 "kb"，默认为 "kb"
        tags (List[str], optional): 知识库标签列表
        ext_config (Dict, optional): 扩展配置

    Returns:
        Dict[str, str]: 包含知识库ID等信息的响应数据
    """
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json"
    }

    # 构建请求数据
    req_data = {
        "parentId": parent_id or 0,
        "name": name or "",
        "description": description or "",
        "avatarUrl": avatar_url or "",
        "vectorModel": vector_model or "",
        "type": kb_type or "kb",
        "tags": tags or [],
    }

    # 添加扩展配置
    if ext_config:
        req_data["ext"] = ext_config

    url = f"{base_url}/api/kb/create"
    response = requests.post(url, headers=headers, json=req_data, timeout=30)

    if response.status_code == 200:
        response_data = response.json()
        if response_data.get("code") == 1:
            # 直接返回响应数据，data字段包含知识库ID
            return response_data
        else:
            raise Exception(f"创建知识库失败: {response_data.get('msg', 'Unknown error')}")
    else:
        raise Exception(f"创建知识库失败: {response.status_code} - {response.text}")

def query_kb_list_api(
    jwt_token: str,
    base_url: str,
    page_num: int = 1,
    page_size: int = 10,
    count: bool = True,
    keywords: str = "",
    parent_id: int = 0,
    scope: int = 0,
    external_params: Optional[Dict] = None
) -> Dict[str, str]:
    """
    查询知识库列表 API

    Args:
        jwt_token (str): JWT 认证令牌
        base_url (str): API 服务基础地址
        page_num (int, optional): 页码，默认1
        page_size (int, optional): 每页大小，默认10
        count (bool, optional): 是否统计总数，默认True
        keywords (str, optional): 模糊查询关键词，默认为空
        parent_id (int, optional): 父文件夹ID，默认0
        scope (int, optional): 查询范围，默认0（全部）
        external_params (Dict, optional): 扩展查询条件

    Returns:
        Dict[str, str]: 分页查询结果
    """
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json"
    }

    req = KbQueryRequest(
        pageNum=page_num,
        pageSize=page_size,
        count=count,
        keywords=keywords,
        parentId=parent_id,
        scope=scope,
        externalParams=external_params or {}
    )

    url = f"{base_url}/kb/query"
    response = requests.post(url, headers=headers, json=req.model_dump(), timeout=30)

    if response.status_code == 200:
        response_data = response.json()
        if response_data.get("code") == 0:
            return response_data
        else:
            raise Exception(f"查询知识库列表失败: {response_data.get('msg', 'Unknown error')}")
    else:
        raise Exception(f"查询知识库列表失败: {response.status_code} - {response.text}")

