import json
import requests
from typing import Optional, List, Dict, Union, IO
from .kb_models import KbCreateRequest, KbUpdateRequest, KbDocumentUploadRequest, KbDocumentDeleteRequest, KbQueryRequest
from .uploader import FileUploader


def create_kb_api(
    jwt_token: str,
    base_url: str,
    kb_name: str,
    kb_description: str = "",
    kb_type: str = "document"
) -> Dict[str, str]:
    """
    创建知识库 API
    
    Args:
        jwt_token (str): JWT 认证令牌
        base_url (str): API 服务基础地址
        kb_name (str): 知识库名称
        kb_description (str, optional): 知识库描述，默认为空
        kb_type (str, optional): 知识库类型，默认为 "document"
        
    Returns:
        Dict[str, str]: 包含知识库ID等信息的响应数据
    """
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json"
    }
    
    req = KbCreateRequest(
        name=kb_name,
        description=kb_description,
        type=kb_type
    )
    
    url = f"{base_url}/api/kb/create"
    response = requests.post(url, headers=headers, json=req.model_dump(), timeout=30)
    
    if response.status_code == 200:
        return response.json().get("data", {})
    else:
        raise Exception(f"创建知识库失败: {response.status_code} - {response.text}")


def get_kb_api(
    jwt_token: str,
    base_url: str,
    kb_id: str
) -> Dict[str, str]:
    """
    获取知识库信息 API
    
    Args:
        jwt_token (str): JWT 认证令牌
        base_url (str): API 服务基础地址
        kb_id (str): 知识库ID
        
    Returns:
        Dict[str, str]: 知识库详细信息
    """
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json"
    }
    
    url = f"{base_url}/api/kb/{kb_id}"
    response = requests.get(url, headers=headers, timeout=30)
    
    if response.status_code == 200:
        return response.json().get("data", {})
    else:
        raise Exception(f"获取知识库失败: {response.status_code} - {response.text}")


def list_kb_api(
    jwt_token: str,
    base_url: str,
    page_size: int = 20,
    page_number: int = 1
) -> List[Dict[str, str]]:
    """
    获取知识库列表 API
    
    Args:
        jwt_token (str): JWT 认证令牌
        base_url (str): API 服务基础地址
        page_size (int, optional): 每页数量，默认20
        page_number (int, optional): 页码，默认1
        
    Returns:
        List[Dict[str, str]]: 知识库列表
    """
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json"
    }
    
    params = {
        "pageSize": page_size,
        "pageNumber": page_number
    }
    
    url = f"{base_url}/api/kb/list"
    response = requests.get(url, headers=headers, params=params, timeout=30)
    
    if response.status_code == 200:
        return response.json().get("data", [])
    else:
        raise Exception(f"获取知识库列表失败: {response.status_code} - {response.text}")


def update_kb_api(
    jwt_token: str,
    base_url: str,
    kb_id: str,
    kb_name: Optional[str] = None,
    kb_description: Optional[str] = None
) -> Dict[str, str]:
    """
    更新知识库信息 API
    
    Args:
        jwt_token (str): JWT 认证令牌
        base_url (str): API 服务基础地址
        kb_id (str): 知识库ID
        kb_name (str, optional): 新的知识库名称
        kb_description (str, optional): 新的知识库描述
        
    Returns:
        Dict[str, str]: 更新后的知识库信息
    """
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json"
    }
    
    req = KbUpdateRequest(
        kbId=kb_id,
        name=kb_name,
        description=kb_description
    )
    
    url = f"{base_url}/api/kb/update"
    response = requests.put(url, headers=headers, json=req.model_dump(exclude_none=True), timeout=30)
    
    if response.status_code == 200:
        return response.json().get("data", {})
    else:
        raise Exception(f"更新知识库失败: {response.status_code} - {response.text}")


def delete_kb_api(
    jwt_token: str,
    base_url: str,
    kb_id: str
) -> bool:
    """
    删除知识库 API
    
    Args:
        jwt_token (str): JWT 认证令牌
        base_url (str): API 服务基础地址
        kb_id (str): 知识库ID
        
    Returns:
        bool: 删除是否成功
    """
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json"
    }
    
    url = f"{base_url}/api/kb/{kb_id}"
    response = requests.delete(url, headers=headers, timeout=30)
    
    if response.status_code == 200:
        return True
    else:
        raise Exception(f"删除知识库失败: {response.status_code} - {response.text}")


def upload_document_api(
    jwt_token: str,
    base_url: str,
    kb_id: str,
    files: Optional[List[Union[str, IO]]] = None,
    content: Optional[str] = None,
    file_name: Optional[str] = None
) -> Dict[str, str]:
    """
    上传文档到知识库 API
    
    Args:
        jwt_token (str): JWT 认证令牌
        base_url (str): API 服务基础地址
        kb_id (str): 知识库ID
        files (List[Union[str, IO]], optional): 文件列表
        content (str, optional): 文本内容
        file_name (str, optional): 文件名（当使用content时必填）
        
    Returns:
        Dict[str, str]: 上传结果信息
    """
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json"
    }
    
    if files:
        # 使用文件上传
        uploader = FileUploader(jwt_token=jwt_token, base_url=base_url)
        file_inputs = uploader.ensure_file_inputs(files)
        
        req = {
            "kbId": kb_id,
            "files": [file_input.model_dump() for file_input in file_inputs]
        }
    elif content and file_name:
        # 使用文本内容
        req = KbDocumentUploadRequest(
            kbId=kb_id,
            fileName=file_name,
            fileContent=content
        )
        req = req.model_dump()
    else:
        raise ValueError("必须提供files或者content+file_name参数")
    
    url = f"{base_url}/api/kb/document/upload"
    response = requests.post(url, headers=headers, json=req, timeout=60)
    
    if response.status_code == 200:
        return response.json().get("data", {})
    else:
        raise Exception(f"上传文档失败: {response.status_code} - {response.text}")


def list_documents_api(
    jwt_token: str,
    base_url: str,
    kb_id: str,
    page_size: int = 20,
    page_number: int = 1
) -> List[Dict[str, str]]:
    """
    获取知识库文档列表 API
    
    Args:
        jwt_token (str): JWT 认证令牌
        base_url (str): API 服务基础地址
        kb_id (str): 知识库ID
        page_size (int, optional): 每页数量，默认20
        page_number (int, optional): 页码，默认1
        
    Returns:
        List[Dict[str, str]]: 文档列表
    """
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json"
    }
    
    params = {
        "kbId": kb_id,
        "pageSize": page_size,
        "pageNumber": page_number
    }
    
    url = f"{base_url}/api/kb/document/list"
    response = requests.get(url, headers=headers, params=params, timeout=30)
    
    if response.status_code == 200:
        return response.json().get("data", [])
    else:
        raise Exception(f"获取文档列表失败: {response.status_code} - {response.text}")


def delete_document_api(
    jwt_token: str,
    base_url: str,
    kb_id: str,
    document_id: str
) -> bool:
    """
    删除知识库文档 API
    
    Args:
        jwt_token (str): JWT 认证令牌
        base_url (str): API 服务基础地址
        kb_id (str): 知识库ID
        document_id (str): 文档ID
        
    Returns:
        bool: 删除是否成功
    """
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json"
    }
    
    req = KbDocumentDeleteRequest(
        kbId=kb_id,
        documentId=document_id
    )
    
    url = f"{base_url}/api/kb/document/delete"
    response = requests.delete(url, headers=headers, json=req.model_dump(), timeout=30)
    
    if response.status_code == 200:
        return True
    else:
        raise Exception(f"删除文档失败: {response.status_code} - {response.text}")


def query_kb_api(
    jwt_token: str,
    base_url: str,
    kb_id: str,
    query: str,
    top_k: int = 5,
    score_threshold: float = 0.0
) -> List[Dict[str, str]]:
    """
    查询知识库 API
    
    Args:
        jwt_token (str): JWT 认证令牌
        base_url (str): API 服务基础地址
        kb_id (str): 知识库ID
        query (str): 查询文本
        top_k (int, optional): 返回结果数量，默认5
        score_threshold (float, optional): 相似度阈值，默认0.0
        
    Returns:
        List[Dict[str, str]]: 查询结果列表
    """
    headers = {
        "Authorization": f"Bearer {jwt_token}",
        "Content-Type": "application/json"
    }
    
    req = KbQueryRequest(
        kbId=kb_id,
        query=query,
        topK=top_k,
        scoreThreshold=score_threshold
    )
    
    url = f"{base_url}/api/kb/query"
    response = requests.post(url, headers=headers, json=req.model_dump(), timeout=30)
    
    if response.status_code == 200:
        return response.json().get("data", [])
    else:
        raise Exception(f"查询知识库失败: {response.status_code} - {response.text}")
