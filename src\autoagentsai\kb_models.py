# src/autoagentsai/kb_models.py
from typing import Optional
from pydantic import BaseModel


class KbCreateRequest(BaseModel):
    """创建知识库请求模型"""
    name: str
    description: Optional[str] = ""
    type: str = "document"  # document, qa, etc.


class KbUpdateRequest(BaseModel):
    """更新知识库请求模型"""
    kbId: str
    name: Optional[str] = None
    description: Optional[str] = None


class KbDocumentUploadRequest(BaseModel):
    """知识库文档上传请求模型"""
    kbId: str
    fileName: str
    fileContent: Optional[str] = None  # 文本内容
    fileUrl: Optional[str] = None      # 文件URL


class KbDocumentDeleteRequest(BaseModel):
    """知识库文档删除请求模型"""
    kbId: str
    documentId: str


class KbQueryRequest(BaseModel):
    """知识库查询请求模型"""
    kbId: str
    query: str
    topK: int = 5
    scoreThreshold: float = 0.0
