#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试知识库修改功能

测试修改知识库的各种场景
"""

from src.autoagentsai.client import KbClient


def test_modify_basic():
    """测试基础修改功能"""
    print("📝 测试基础修改功能...")
    
    kb_client = KbClient(
        personal_auth_key="135c9b6f7660456ba14a2818a311a80e",
        personal_auth_secret="i34ia5UpBnjuW42huwr97xTiFlIyeXc7"
    )
    
    try:
        # 首先获取一个知识库ID
        kb_list_result = kb_client.query_kb_list(page_size=1)
        if kb_list_result.get("code") == 0:
            kb_list = kb_list_result["data"]["list"]
            if kb_list:
                kb_id = kb_list[0]["id"]
                original_name = kb_list[0]["name"]
                
                print(f"📚 使用知识库: {original_name} (ID: {kb_id})")
                
                # 修改名称和描述
                new_name = f"{original_name} - 已修改"
                new_description = "这是通过API修改的描述信息"
                
                result = kb_client.modify_kb(
                    kb_id=kb_id,
                    name=new_name,
                    description=new_description
                )
                
                print(f"✅ 修改成功: {result}")
                print(f"📄 响应码: {result.get('code')}")
                print(f"💬 消息: {result.get('msg')}")
                
                return kb_id
            else:
                print("⚠️ 没有找到可用的知识库")
        else:
            print("❌ 无法获取知识库列表")
            
    except Exception as e:
        print(f"❌ 基础修改测试失败: {str(e)}")
        
    return None


def test_modify_tags():
    """测试修改标签"""
    print("\n🏷️ 测试修改标签...")
    
    kb_client = KbClient(
        personal_auth_key="135c9b6f7660456ba14a2818a311a80e",
        personal_auth_secret="i34ia5UpBnjuW42huwr97xTiFlIyeXc7"
    )
    
    try:
        # 获取知识库
        kb_list_result = kb_client.query_kb_list(page_size=1)
        if kb_list_result.get("code") == 0:
            kb_list = kb_list_result["data"]["list"]
            if kb_list:
                kb_id = kb_list[0]["id"]
                
                # 修改标签
                new_tags = ["测试标签", "API修改", "自动化测试"]
                
                result = kb_client.modify_kb(
                    kb_id=kb_id,
                    tags=new_tags
                )
                
                print(f"✅ 标签修改成功")
                print(f"🏷️ 新标签: {new_tags}")
                print(f"📄 响应: {result.get('msg')}")
                
    except Exception as e:
        print(f"❌ 标签修改测试失败: {str(e)}")


def test_modify_avatar():
    """测试修改头像"""
    print("\n🖼️ 测试修改头像...")
    
    kb_client = KbClient(
        personal_auth_key="135c9b6f7660456ba14a2818a311a80e",
        personal_auth_secret="i34ia5UpBnjuW42huwr97xTiFlIyeXc7"
    )
    
    try:
        # 获取知识库
        kb_list_result = kb_client.query_kb_list(page_size=1)
        if kb_list_result.get("code") == 0:
            kb_list = kb_list_result["data"]["list"]
            if kb_list:
                kb_id = kb_list[0]["id"]
                
                # 修改头像URL
                new_avatar_url = "https://example.com/new-avatar.png"
                
                result = kb_client.modify_kb(
                    kb_id=kb_id,
                    avatar_url=new_avatar_url
                )
                
                print(f"✅ 头像修改成功")
                print(f"🖼️ 新头像URL: {new_avatar_url}")
                print(f"📄 响应: {result.get('msg')}")
                
    except Exception as e:
        print(f"❌ 头像修改测试失败: {str(e)}")


def test_modify_ext_config():
    """测试修改扩展配置"""
    print("\n🔧 测试修改扩展配置...")
    
    kb_client = KbClient(
        personal_auth_key="135c9b6f7660456ba14a2818a311a80e",
        personal_auth_secret="i34ia5UpBnjuW42huwr97xTiFlIyeXc7"
    )
    
    try:
        # 获取知识库
        kb_list_result = kb_client.query_kb_list(page_size=1)
        if kb_list_result.get("code") == 0:
            kb_list = kb_list_result["data"]["list"]
            if kb_list:
                kb_id = kb_list[0]["id"]
                
                # 修改扩展配置
                new_ext_config = {
                    "configWay": "auto",
                    "chunkSize": 600,
                    "coverageRate": 0.9,
                    "similarity": 0.8,
                    "limit": 3000,
                    "language": "zh",
                    "parserType": "auto",
                    "contentEnhances": ["summary", "keyword", "ocr"],
                    "search": {
                        "vectorSimilarLimit": 0.9,
                        "vectorSimilarWeight": 0.6,
                        "topK": 10,
                        "enableRerank": True,
                        "rerankModelType": "bge-rerank-v2",
                        "rerankSimilarLimit": 0.95,
                        "rerankTopK": 5
                    }
                }
                
                result = kb_client.modify_kb(
                    kb_id=kb_id,
                    ext_config=new_ext_config
                )
                
                print(f"✅ 扩展配置修改成功")
                print(f"🔧 新配置: 分块大小={new_ext_config['chunkSize']}, 相似度={new_ext_config['similarity']}")
                print(f"📄 响应: {result.get('msg')}")
                
    except Exception as e:
        print(f"❌ 扩展配置修改测试失败: {str(e)}")


def test_modify_multiple():
    """测试同时修改多个属性"""
    print("\n🔄 测试同时修改多个属性...")
    
    kb_client = KbClient(
        personal_auth_key="135c9b6f7660456ba14a2818a311a80e",
        personal_auth_secret="i34ia5UpBnjuW42huwr97xTiFlIyeXc7"
    )
    
    try:
        # 获取知识库
        kb_list_result = kb_client.query_kb_list(page_size=1)
        if kb_list_result.get("code") == 0:
            kb_list = kb_list_result["data"]["list"]
            if kb_list:
                kb_id = kb_list[0]["id"]
                
                # 同时修改多个属性
                result = kb_client.modify_kb(
                    kb_id=kb_id,
                    name="综合测试知识库",
                    description="这是一个综合修改测试的知识库",
                    tags=["综合测试", "多属性修改", "API测试"],
                    avatar_url="https://example.com/comprehensive-test.png"
                )
                
                print(f"✅ 多属性修改成功")
                print(f"📄 响应: {result.get('msg')}")
                
    except Exception as e:
        print(f"❌ 多属性修改测试失败: {str(e)}")


def test_modify_error_cases():
    """测试错误情况"""
    print("\n⚠️ 测试错误情况...")
    
    kb_client = KbClient(
        personal_auth_key="135c9b6f7660456ba14a2818a311a80e",
        personal_auth_secret="i34ia5UpBnjuW42huwr97xTiFlIyeXc7"
    )
    
    # 测试1: 不提供任何修改参数
    try:
        result = kb_client.modify_kb(kb_id=123456)
        print("❌ 应该抛出异常但没有")
    except ValueError as e:
        print(f"✅ 正确捕获参数错误: {str(e)}")
    except Exception as e:
        print(f"⚠️ 其他异常: {str(e)}")
    
    # 测试2: 使用不存在的知识库ID
    try:
        result = kb_client.modify_kb(
            kb_id=999999999,
            name="不存在的知识库"
        )
        print(f"⚠️ 意外成功: {result}")
    except Exception as e:
        print(f"✅ 正确处理不存在的知识库: {str(e)[:100]}...")


def main():
    """主测试函数"""
    print("🧪 知识库修改功能测试")
    print("=" * 50)
    
    # 运行各项测试
    tests = [
        test_modify_basic,
        test_modify_tags,
        test_modify_avatar,
        test_modify_ext_config,
        test_modify_multiple,
        test_modify_error_cases
    ]
    
    success_count = 0
    for test_func in tests:
        try:
            result = test_func()
            success_count += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {success_count}/{len(tests)} 通过")
    
    if success_count == len(tests):
        print("🎉 所有测试通过！修改功能正常！")
        print("\n✨ 支持的修改操作:")
        print("   - 修改知识库名称和描述")
        print("   - 更新标签列表")
        print("   - 修改头像URL")
        print("   - 更新扩展配置")
        print("   - 同时修改多个属性")
    else:
        print("⚠️ 部分测试失败，请检查API配置")


if __name__ == "__main__":
    main()
