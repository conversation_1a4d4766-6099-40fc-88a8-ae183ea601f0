# src/autoagentsai/types.py
from typing import Optional, List, Dict, Union, IO
from pydantic import BaseModel, Field


class ImageInput(BaseModel):
    url: str


class FileInput(BaseModel):
    groupName: str = ""
    dsId: int = 0
    fileId: str
    fileName: str
    fileUrl: str = ""
    fileType: str = ""


class ChatRequest(BaseModel):
    agentId: str
    chatId: Optional[str] = None
    userChatInput: str
    images: Optional[List[ImageInput]] = Field(default_factory=list)
    files: Optional[List[FileInput]] = Field(default_factory=list)
    state: Optional[Dict[str, str]] = Field(default_factory=dict)
    buttonKey: Optional[str] = ""
    debug: Optional[bool] = False

class ChatHistoryRequest(BaseModel):
    agentId: Optional[str] = None
    agentUUid: str
    chatId: str
    pageSize: int = 100
    pageNumber: int = 1


# 知识库相关模型
class KbCreateRequest(BaseModel):
    """创建知识库请求模型"""
    name: str
    description: Optional[str] = ""
    type: str = "document"  # document, qa, etc.


class KbUpdateRequest(BaseModel):
    """更新知识库请求模型"""
    kbId: str
    name: Optional[str] = None
    description: Optional[str] = None


class KbDocumentUploadRequest(BaseModel):
    """知识库文档上传请求模型"""
    kbId: str
    fileName: str
    fileContent: Optional[str] = None  # 文本内容
    fileUrl: Optional[str] = None      # 文件URL


class KbDocumentDeleteRequest(BaseModel):
    """知识库文档删除请求模型"""
    kbId: str
    documentId: str


class KbQueryRequest(BaseModel):
    """知识库查询请求模型"""
    kbId: str
    query: str
    topK: int = 5
    scoreThreshold: float = 0.0