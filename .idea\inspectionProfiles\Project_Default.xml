<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="5">
            <item index="0" class="java.lang.String" itemvalue="pandas" />
            <item index="1" class="java.lang.String" itemvalue="scipy" />
            <item index="2" class="java.lang.String" itemvalue="matplotlib" />
            <item index="3" class="java.lang.String" itemvalue="contourpy" />
            <item index="4" class="java.lang.String" itemvalue="gradio" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPep8NamingInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true">
      <option name="ignoredErrors">
        <list>
          <option value="N803" />
          <option value="N801" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>