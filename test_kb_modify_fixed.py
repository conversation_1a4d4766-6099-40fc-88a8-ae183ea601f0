#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的知识库修改功能
"""

from src.autoagentsai.client import KbClient


def test_simple_modify():
    """测试简单修改"""
    print("🧪 测试修复后的修改功能...")
    
    kb_client = KbClient(
        personal_auth_key="135c9b6f7660456ba14a2818a311a80e",
        personal_auth_secret="i34ia5UpBnjuW42huwr97xTiFlIyeXc7"
    )
    
    try:
        # 获取知识库列表
        print("1. 获取知识库列表...")
        kb_list_result = kb_client.query_kb_list(page_size=3)
        
        if kb_list_result.get("code") == 0:
            kb_list = kb_list_result["data"]["list"]
            print(f"✅ 找到 {len(kb_list)} 个知识库")
            
            # 查找有编辑权限的知识库
            target_kb = None
            for kb in kb_list:
                permissions = kb.get("kbBtnPermission", [])
                if "edit" in permissions:
                    target_kb = kb
                    break
            
            if target_kb:
                kb_id = target_kb["id"]
                kb_name = target_kb["name"]
                print(f"✅ 使用知识库: {kb_name} (ID: {kb_id})")
                print(f"   权限: {target_kb.get('kbBtnPermission', [])}")
                
                # 测试修改
                print(f"\n2. 测试修改知识库...")
                
                # 只修改描述
                new_description = f"修改测试 - {target_kb.get('description', '')}"
                
                result = kb_client.modify_kb(
                    kb_id=kb_id,
                    description=new_description
                )
                
                print(f"✅ 修改成功!")
                print(f"📄 响应: {result}")
                
            else:
                print("⚠️ 没有找到有编辑权限的知识库")
                
                # 显示所有知识库的权限信息
                print("\n所有知识库权限信息:")
                for kb in kb_list:
                    print(f"- {kb['name']} (ID: {kb['id']})")
                    print(f"  权限: {kb.get('kbBtnPermission', [])}")
                    print(f"  作者: {kb.get('authorName', 'Unknown')}")
                    
        else:
            print(f"❌ 获取知识库列表失败: {kb_list_result}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


def test_different_modifications():
    """测试不同类型的修改"""
    print("\n🔧 测试不同类型的修改...")
    
    kb_client = KbClient(
        personal_auth_key="135c9b6f7660456ba14a2818a311a80e",
        personal_auth_secret="i34ia5UpBnjuW42huwr97xTiFlIyeXc7"
    )
    
    try:
        # 获取有编辑权限的知识库
        kb_list_result = kb_client.query_kb_list(page_size=5)
        
        if kb_list_result.get("code") == 0:
            kb_list = kb_list_result["data"]["list"]
            
            target_kb = None
            for kb in kb_list:
                if "edit" in kb.get("kbBtnPermission", []):
                    target_kb = kb
                    break
            
            if target_kb:
                kb_id = target_kb["id"]
                
                # 测试不同的修改
                modifications = [
                    {
                        "name": "修改名称",
                        "params": {"name": f"{target_kb['name']} - 测试修改"}
                    },
                    {
                        "name": "修改描述",
                        "params": {"description": "这是通过API修改的新描述"}
                    },
                    {
                        "name": "修改标签",
                        "params": {"tags": ["测试标签", "API修改"]}
                    },
                    {
                        "name": "修改头像",
                        "params": {"avatar_url": "https://example.com/test-avatar.png"}
                    }
                ]
                
                for mod in modifications:
                    print(f"\n测试: {mod['name']}")
                    try:
                        result = kb_client.modify_kb(
                            kb_id=kb_id,
                            **mod['params']
                        )
                        print(f"✅ {mod['name']} 成功: {result.get('msg')}")
                    except Exception as e:
                        print(f"❌ {mod['name']} 失败: {str(e)}")
                        
            else:
                print("⚠️ 没有找到可编辑的知识库")
                
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")


if __name__ == "__main__":
    print("🧪 修复后的知识库修改功能测试")
    print("=" * 50)
    
    test_simple_modify()
    test_different_modifications()
    
    print("\n" + "=" * 50)
    print("🔧 修复说明:")
    print("1. 确保知识库ID为整数类型")
    print("2. 请求体包含所有字段（即使是None）")
    print("3. 标签字段默认为空数组而不是None")
    print("4. 添加了详细的调试信息")
    print("5. 检查知识库编辑权限")
